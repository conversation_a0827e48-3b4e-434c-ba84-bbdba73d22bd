/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./src/**/*.{js,jsx,ts,tsx}",
        "../shared-ui/src/**/*.{js,jsx,ts,tsx}",
        "node_modules/@senseproof/shared-ui/**/*.{js,ts,jsx,tsx}"
    ],
    theme: {
        extend: {
            colors: {
                paper: {
                    DEFAULT: '#ffffff',
                    off: '#f7f7f7',
                },
                ink: {
                    primary: '#1a1a1a',
                    secondary: '#5e6c76',
                    disabled: '#a3a3a3',
                },
                stroke: {
                    DEFAULT: '#d1d5db',
                    light: '#e5e7eb',
                },
            },
            boxShadow: {
                'sm': '0 2px 4px 0 rgba(0,0,0,0.05)',
            },
            borderRadius: {
                'md': '4px',
            },
        }
    },
    plugins: []
}
