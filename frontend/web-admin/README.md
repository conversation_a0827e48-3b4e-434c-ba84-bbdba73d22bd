# Senseproof Shared UI Library

This is the shared component library for the Senseproof platform. It contains reusable UI components built with React, TypeScript, and styled with Tailwind CSS. This library is developed and visualized using Storybook.

# Getting Started

To get started with developing components in this library, first clone the repository and then install the necessary dependencies.

## Install all project dependencies
```npm install```

## Available Scripts

In the project directory, you can run the following commands:
```npm run storybook```

Launches the Storybook development environment. This is the primary way to visualize and develop components in isolation.

Open http://localhost:6006 to view it in your browser. The page will reload when you make changes to component files or their stories.

```npm test```

## Launches the Jest test runner in interactive watch mode.

This command will execute all test files (.test.tsx) to ensure that components work as expected. It's recommended to run this before committing changes.

```npm run build```

## Builds the library for production.

This command compiles the TypeScript code and bundles it into a dist folder. It creates:

    JavaScript files in different formats (ESM, UMD).

    TypeScript declaration files (.d.ts).

    A single style.css file containing all the necessary Tailwind CSS styles.

The dist folder is what gets published or linked to other projects.
## How to Use This Library in Another Project

To use the components from this library in another application (e.g., web-client or web-admin):

    Link or Install the Package:

        If you are working in a monorepo (with local packages), ensure your package.json in the consuming project has a dependency on @senseproof/shared-ui.

        If you publish this library to a registry like npm, you would install it via npm install @senseproof/shared-ui.

    Import the Component:
    You can import any exported component directly from the library.

    import { SpButton } from '@senseproof/shared-ui';

    function MyComponent() {
      return (
        <SpButton variant="primary" size="large">
          Click Me
        </SpButton>
      );
    }

    Import the CSS file (Crucial Step!):
    For the styles (like variant and size) to work, you must import the library's CSS file once in your main application entry point (e.g., main.tsx or App.tsx).

    // In your main application file (e.g., web-client/src/main.tsx)
    import React from 'react';
    import ReactDOM from 'react-dom/client';
    import App from './App';

    // Import the shared UI library's styles
    import '@senseproof/shared-ui/dist/style.css'; 

    ReactDOM.createRoot(document.getElementById('root')!).render(
      <React.StrictMode>
        <App />
      </React.StrictMode>,
    );

## Adding a New Component

To add a new component (e.g., SpInput) to this library, follow these steps:

    Create a new folder under src/components/, for example src/components/sp-input/.

    Inside the new folder, create the necessary files:

        SpInput.tsx (The component itself)

        types.ts (TypeScript interfaces for props)

        styles.ts (Styling logic)

        SpInput.stories.tsx (The Storybook story to visualize it)

        test/sp-input.test.tsx (Tests for the component)

    Export the new component from the library's main entry point, src/index.ts:

    // in src/index.ts
    export * from './components/sp-button';
    export * from './components/sp-input'; // <-- Add the new export

    Run npm run build to make sure the new component is included in the production build.
