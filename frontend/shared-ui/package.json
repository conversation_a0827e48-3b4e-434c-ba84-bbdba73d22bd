{"name": "@senseproof/shared-ui", "version": "1.0.2", "main": "src/index.ts", "types": "src/index.ts", "dependencies": {"clsx": "^2.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "rollup-plugin-copy": "^3.5.0", "vite-plugin-dts": "^4.5.4"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@chromatic-com/storybook": "^4.0.0", "@storybook/addon-a11y": "^9.0.5", "@storybook/addon-docs": "^9.0.5", "@storybook/addon-links": "^9.0.5", "@storybook/addon-onboarding": "^9.0.5", "@storybook/addon-vitest": "^9.0.5", "@storybook/react-vite": "^9.0.5", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^18.2.14", "@vitest/browser": "^3.2.2", "@vitest/coverage-v8": "^3.2.2", "autoprefixer": "^10.4.14", "babel-jest": "^30.0.0-beta.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "playwright": "^1.52.0", "postcss": "^8.4.24", "storybook": "^9.0.5", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vitest": "^3.2.2"}, "scripts": {"storybook": "storybook dev -p 6006", "test": "jest", "build-storybook": "storybook build", "build": "tsc && vite build"}, "files": ["dist", "dist/index.css", "src"], "overrides": {"storybook": "$storybook"}}