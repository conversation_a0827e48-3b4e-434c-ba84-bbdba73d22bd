// vite.config.ts
import { resolve } from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import copy from "rollup-plugin-copy";

export default defineConfig({
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
    }),
    copy({
      targets: [
        { src: "src/index.css", dest: "dist" }
      ]
    }),
  ],
  build: {
    // This 'lib' section is the key part that fixes the error
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'SharedUi',
      fileName: (format) => `shared-ui.${format}.js`,
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
      },
    },
  },
});
