import React from "react";
import { TextInputProps } from "./types";
import { inputStyles, labelStyles, helperTextStyles } from "./styles";

type SpTextInputProps = TextInputProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'>;

export const SpTextInput: React.FC<SpTextInputProps> = ({
  value,
  onChange,
  placeholder,
  disabled,
  variant = 'primary',
  size = 'large',
  label,
  error,
  helperText,
  ...rest
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const hasError = Boolean(error);
  const displayHelperText = error || helperText;

  return (
    <div className="w-full">
      {label && (
        <label className={labelStyles({ disabled, hasError })}>
          {label}
        </label>
      )}
      <input
        type="text"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={inputStyles({ disabled, variant, size, hasError })}
        {...rest}
      />
      {displayHelperText && (
        <div className={helperTextStyles({ hasError })}>
          {displayHelperText}
        </div>
      )}
    </div>
  );
};
