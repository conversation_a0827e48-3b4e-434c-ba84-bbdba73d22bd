import React from "react";
import '@testing-library/jest-dom';
import { render, screen, fireEvent } from "@testing-library/react";
import { SpTextInput } from "../index";

describe("SpTextInput", () => {
  test("renders with placeholder", () => {
    render(<SpTextInput placeholder="Enter text" />);
    expect(screen.getByPlaceholderText("Enter text")).toBeInTheDocument();
  });

  test("renders with label", () => {
    render(<SpTextInput label="Username" />);
    expect(screen.getByText("Username")).toBeInTheDocument();
  });

  test("calls onChange when value changes", () => {
    const handleChange = jest.fn();
    render(<SpTextInput onChange={handleChange} />);
    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "test value" } });
    expect(handleChange).toHaveBeenCalledWith("test value");
  });

  test("displays controlled value", () => {
    render(<SpTextInput value="controlled value" onChange={() => {}} />);
    const input = screen.getByRole("textbox") as HTMLInputElement;
    expect(input.value).toBe("controlled value");
  });

  test("disabled input doesn't trigger onChange", () => {
    const handleChange = jest.fn();
    render(<SpTextInput onChange={handleChange} disabled />);
    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "test" } });
    expect(handleChange).not.toHaveBeenCalled();
  });

  test("applies primary styles by default", () => {
    render(<SpTextInput />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("border-stroke");
  });

  test("applies secondary styles when variant is secondary", () => {
    render(<SpTextInput variant="secondary" />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("border-stroke-light");
  });

  test("applies large size styles by default", () => {
    render(<SpTextInput />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("px-4", "py-2", "text-base");
  });

  test("applies regular size styles", () => {
    render(<SpTextInput size="regular" />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("px-3", "py-1.5", "text-sm");
  });

  test("applies small size styles", () => {
    render(<SpTextInput size="small" />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("px-2", "py-1", "text-xs");
  });

  test("displays error message", () => {
    render(<SpTextInput error="This field is required" />);
    expect(screen.getByText("This field is required")).toBeInTheDocument();
  });

  test("displays helper text", () => {
    render(<SpTextInput helperText="Enter your username" />);
    expect(screen.getByText("Enter your username")).toBeInTheDocument();
  });

  test("error takes precedence over helper text", () => {
    render(<SpTextInput error="Error message" helperText="Helper text" />);
    expect(screen.getByText("Error message")).toBeInTheDocument();
    expect(screen.queryByText("Helper text")).not.toBeInTheDocument();
  });

  test("applies error styles when error is present", () => {
    render(<SpTextInput error="Error message" />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveClass("border-red-500");
  });
});
