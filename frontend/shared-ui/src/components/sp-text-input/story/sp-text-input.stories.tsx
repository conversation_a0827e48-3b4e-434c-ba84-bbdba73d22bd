// @ts-nocheck
import type { Meta, StoryObj } from '@storybook/react';
import { SpTextInput } from '../SpTextInput';

const meta: Meta<typeof SpTextInput> = {
  title: 'Components/SpTextInput',
  component: SpTextInput,
  tags: ['autodocs'],
  argTypes: {
    value: { control: 'text', description: 'The current value of the input' },
    placeholder: { control: 'text', description: 'Placeholder text for the input' },
    label: { control: 'text', description: 'Label for the input field' },
    helperText: { control: 'text', description: 'Helper text displayed below the input' },
    error: { control: 'text', description: 'Error message to display' },
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
      description: 'The visual style of the input'
    },
    size: {
      control: { type: 'select' },
      options: ['large', 'regular', 'small'],
      description: 'The size of the input'
    },
    disabled: { control: 'boolean', description: 'Whether the input is disabled' },
    onChange: { action: 'changed', description: 'Callback fired when the value changes' },
  },
  parameters: {
    docs: {
      description: {
        component: 'A text input component with consistent styling and validation support.'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof SpTextInput>;

export const LargePrimary: Story = {
  name: 'Primary (Large)',
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Enter text here...',
    label: 'Username',
    helperText: 'Choose a unique username',
    disabled: false,
  },
};

export const RegularPrimary: Story = {
  name: 'Primary (Regular)',
  args: {
    variant: 'primary',
    size: 'regular',
    placeholder: 'Enter text here...',
    label: 'Username',
    disabled: false,
  },
};

export const SmallPrimary: Story = {
  name: 'Primary (Small)',
  args: {
    variant: 'primary',
    size: 'small',
    placeholder: 'Enter text...',
    label: 'Username',
    disabled: false,
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    size: 'large',
    placeholder: 'Enter text here...',
    label: 'Description',
    helperText: 'Optional field',
    disabled: false,
  },
};

export const WithError: Story = {
  name: 'With Error',
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Enter text here...',
    label: 'Username',
    error: 'This field is required',
    value: '',
    disabled: false,
  },
};

export const Disabled: Story = {
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Enter text here...',
    label: 'Username',
    value: 'Disabled input',
    disabled: true,
  },
};

export const WithValue: Story = {
  name: 'With Value',
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Enter text here...',
    label: 'Username',
    value: 'john_doe',
    helperText: 'Username is available',
    disabled: false,
  },
};

export const NoLabel: Story = {
  name: 'Without Label',
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Search...',
    disabled: false,
  },
};
