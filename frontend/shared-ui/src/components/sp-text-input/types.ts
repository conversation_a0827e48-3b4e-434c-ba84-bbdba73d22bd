import React from "react";

export interface TextInputProps {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    disabled?: boolean;
    /**
     * The visual style of the input.
     * `primary`: Main input with a solid border.
     * `secondary`: Secondary input with a lighter border.
     * @default 'primary'
     */
    variant?: 'primary' | 'secondary';
    /**
     * The size of the input.
     * @default 'large'
     */
    size?: 'large' | 'regular' | 'small';
    /**
     * Label for the input field
     */
    label?: string;
    /**
     * Error message to display
     */
    error?: string;
    /**
     * Helper text to display below the input
     */
    helperText?: string;
}
