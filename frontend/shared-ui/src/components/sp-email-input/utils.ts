/**
 * Validates email format using a comprehensive regex pattern
 * @param email - The email string to validate
 * @returns true if email is valid, false otherwise
 */
export const isValidEmail = (email: string): boolean => {
  if (!email || email.trim() === '') {
    return false;
  }

  const trimmedEmail = email.trim();

  // Basic checks
  if (trimmedEmail.includes(' ')) return false;
  if (trimmedEmail.includes('..')) return false;
  if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) return false;
  if (trimmedEmail.startsWith('@') || trimmedEmail.endsWith('@')) return false;
  if (!trimmedEmail.includes('@')) return false;

  const parts = trimmedEmail.split('@');
  if (parts.length !== 2) return false;

  const [localPart, domainPart] = parts;
  if (!localPart || !domainPart) return false;

  // Domain must have at least one dot and valid structure
  if (!domainPart.includes('.')) return false;
  if (domainPart.startsWith('.') || domainPart.endsWith('.')) return false;
  if (domainPart.includes('..')) return false;

  // Comprehensive email regex pattern
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  return emailRegex.test(trimmedEmail);
};

/**
 * Validates email and returns error message if invalid
 * @param email - The email string to validate
 * @returns Error message string or null if valid
 */
export const validateEmail = (email: string): string | null => {
  if (!email || email.trim() === '') {
    return 'Email is required';
  }

  if (!isValidEmail(email)) {
    return 'Please enter a valid email address';
  }

  return null;
};
