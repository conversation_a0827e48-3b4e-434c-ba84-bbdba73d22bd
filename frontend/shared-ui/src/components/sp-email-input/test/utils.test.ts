import { isValidEmail, validateEmail } from "../utils";

describe("Email validation utils", () => {
  describe("isValidEmail", () => {
    test("returns true for valid email addresses", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    test("returns false for invalid email addresses", () => {
      const invalidEmails = [
        "",
        "   ",
        "invalid",
        "@example.com",
        "user@",
        "<EMAIL>",
        "<EMAIL>",
        "user@example.",
        "user <EMAIL>",
        "user@exam ple.com",
      ];

      invalidEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(false);
      });
    });

    test("handles whitespace correctly", () => {
      expect(isValidEmail("  <EMAIL>  ")).toBe(true);
      expect(isValidEmail("user @example.com")).toBe(false);
      expect(isValidEmail("user@ example.com")).toBe(false);
    });
  });

  describe("validateEmail", () => {
    test("returns null for valid email addresses", () => {
      expect(validateEmail("<EMAIL>")).toBeNull();
      expect(validateEmail("<EMAIL>")).toBeNull();
    });

    test("returns error message for empty email", () => {
      expect(validateEmail("")).toBe("Email is required");
      expect(validateEmail("   ")).toBe("Email is required");
    });

    test("returns error message for invalid email format", () => {
      expect(validateEmail("invalid")).toBe("Please enter a valid email address");
      expect(validateEmail("user@")).toBe("Please enter a valid email address");
      expect(validateEmail("@example.com")).toBe("Please enter a valid email address");
    });

    test("handles whitespace in validation", () => {
      expect(validateEmail("  <EMAIL>  ")).toBeNull();
    });
  });
});
