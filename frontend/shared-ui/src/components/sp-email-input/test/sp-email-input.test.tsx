import React from "react";
import '@testing-library/jest-dom';
import { render, screen, fireEvent } from "@testing-library/react";
import { SpEmailInput } from "../index";

describe("SpEmailInput", () => {
  test("renders with default email placeholder", () => {
    render(<SpEmailInput />);
    expect(screen.getByPlaceholderText("Enter your email address")).toBeInTheDocument();
  });

  test("renders with custom placeholder", () => {
    render(<SpEmailInput placeholder="Your email" />);
    expect(screen.getByPlaceholderText("Your email")).toBeInTheDocument();
  });

  test("renders with label", () => {
    render(<SpEmailInput label="Email Address" />);
    expect(screen.getByText("Email Address")).toBeInTheDocument();
  });

  test("has email input type", () => {
    render(<SpEmailInput />);
    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("type", "email");
  });

  test("calls onChange when value changes", () => {
    const handleChange = jest.fn();
    render(<SpEmailInput onChange={handleChange} />);
    const input = screen.getByRole("textbox");
    fireEvent.change(input, { target: { value: "<EMAIL>" } });
    expect(handleChange).toHaveBeenCalledWith("<EMAIL>");
  });

  test("displays controlled value", () => {
    render(<SpEmailInput value="<EMAIL>" onChange={() => {}} />);
    const input = screen.getByRole("textbox") as HTMLInputElement;
    expect(input.value).toBe("<EMAIL>");
  });

  test("validates email on blur by default", () => {
    render(<SpEmailInput value="invalid-email" onChange={() => {}} />);
    const input = screen.getByRole("textbox");

    fireEvent.blur(input);

    expect(screen.getByText("Please enter a valid email address")).toBeInTheDocument();
  });

  test("validates email on change when validateOn is 'change'", () => {
    render(<SpEmailInput validateOn="change" />);
    const input = screen.getByRole("textbox");
    
    // First blur to mark as touched
    fireEvent.blur(input);
    
    fireEvent.change(input, { target: { value: "invalid-email" } });
    
    expect(screen.getByText("Please enter a valid email address")).toBeInTheDocument();
  });

  test("shows required error for empty email", () => {
    render(<SpEmailInput />);
    const input = screen.getByRole("textbox");
    
    fireEvent.change(input, { target: { value: "" } });
    fireEvent.blur(input);
    
    expect(screen.getByText("Email is required")).toBeInTheDocument();
  });

  test("accepts valid email addresses", () => {
    render(<SpEmailInput value="<EMAIL>" onChange={() => {}} />);
    const input = screen.getByRole("textbox");

    fireEvent.blur(input);

    expect(screen.queryByText("Please enter a valid email address")).not.toBeInTheDocument();
    expect(screen.queryByText("Email is required")).not.toBeInTheDocument();
  });

  test("uses custom validation when provided", () => {
    const customValidation = (email: string) => {
      if (email.includes("forbidden")) {
        return "This email domain is not allowed";
      }
      return null;
    };

    render(<SpEmailInput customValidation={customValidation} />);
    const input = screen.getByRole("textbox");
    
    fireEvent.change(input, { target: { value: "<EMAIL>" } });
    fireEvent.blur(input);
    
    expect(screen.getByText("This email domain is not allowed")).toBeInTheDocument();
  });

  test("applies error styles when validation fails", () => {
    render(<SpEmailInput />);
    const input = screen.getByRole("textbox");
    
    fireEvent.change(input, { target: { value: "invalid-email" } });
    fireEvent.blur(input);
    
    expect(input).toHaveClass("border-red-500");
  });

  test("displays helper text when no error", () => {
    render(<SpEmailInput helperText="We'll never share your email" />);
    expect(screen.getByText("We'll never share your email")).toBeInTheDocument();
  });

  test("error message takes precedence over helper text", () => {
    render(<SpEmailInput helperText="Helper text" />);
    const input = screen.getByRole("textbox");
    
    fireEvent.change(input, { target: { value: "invalid" } });
    fireEvent.blur(input);
    
    expect(screen.getByText("Please enter a valid email address")).toBeInTheDocument();
    expect(screen.queryByText("Helper text")).not.toBeInTheDocument();
  });

  test("disabled input doesn't trigger validation", () => {
    const handleChange = jest.fn();
    render(<SpEmailInput onChange={handleChange} disabled />);
    const input = screen.getByRole("textbox");
    
    fireEvent.change(input, { target: { value: "test" } });
    fireEvent.blur(input);
    
    expect(handleChange).not.toHaveBeenCalled();
    expect(screen.queryByText("Please enter a valid email address")).not.toBeInTheDocument();
  });
});
