import React from "react";

export interface EmailInputProps {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    disabled?: boolean;
    /**
     * The visual style of the input.
     * `primary`: Main input with a solid border.
     * `secondary`: Secondary input with a lighter border.
     * @default 'primary'
     */
    variant?: 'primary' | 'secondary';
    /**
     * The size of the input.
     * @default 'large'
     */
    size?: 'large' | 'regular' | 'small';
    /**
     * Label for the input field
     */
    label?: string;
    /**
     * Helper text to display below the input
     */
    helperText?: string;
    /**
     * Custom validation function
     */
    customValidation?: (email: string) => string | null;
    /**
     * Whether to show validation on blur or on change
     * @default 'blur'
     */
    validateOn?: 'blur' | 'change';
}
