import React, { useState, useCallback } from "react";
import { EmailInputProps } from "./types";
import { inputStyles, labelStyles, helperTextStyles } from "./styles";
import { validateEmail } from "./utils";

type SpEmailInputProps = EmailInputProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value' | 'type'>;

export const SpEmailInput: React.FC<SpEmailInputProps> = ({
  value,
  onChange,
  placeholder = "Enter your email address",
  disabled,
  variant = 'primary',
  size = 'large',
  label,
  helperText,
  customValidation,
  validateOn = 'blur',
  ...rest
}) => {
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState(false);

  const validateEmailValue = useCallback((email: string): string | null => {
    if (customValidation) {
      return customValidation(email);
    }
    return validateEmail(email);
  }, [customValidation]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    
    if (onChange) {
      onChange(newValue);
    }

    // Validate on change if specified
    if (validateOn === 'change' && touched) {
      const validationError = validateEmailValue(newValue);
      setError(validationError);
    }
  };

  const handleBlur = () => {
    setTouched(true);
    
    // Validate on blur (default behavior)
    if (validateOn === 'blur' && value !== undefined) {
      const validationError = validateEmailValue(value);
      setError(validationError);
    }
  };

  const hasError = Boolean(error);
  const displayHelperText = error || helperText;

  return (
    <div className="w-full">
      {label && (
        <label className={labelStyles({ disabled, hasError })}>
          {label}
        </label>
      )}
      <input
        type="email"
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        className={inputStyles({ disabled, variant, size, hasError })}
        {...rest}
      />
      {displayHelperText && (
        <div className={helperTextStyles({ hasError })}>
          {displayHelperText}
        </div>
      )}
    </div>
  );
};
