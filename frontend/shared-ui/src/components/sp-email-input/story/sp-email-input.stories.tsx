import type { Meta, StoryObj } from '@storybook/react';
import { SpEmailInput } from '../SpEmailInput';

const meta: Meta<typeof SpEmailInput> = {
  title: 'Components/SpEmailInput',
  component: SpEmailInput,
  tags: ['autodocs'],
  argTypes: {
    value: { control: 'text', description: 'The current email value' },
    placeholder: { control: 'text', description: 'Placeholder text for the email input' },
    label: { control: 'text', description: 'Label for the email input field' },
    helperText: { control: 'text', description: 'Helper text displayed below the input' },
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
      description: 'The visual style of the input'
    },
    size: {
      control: { type: 'select' },
      options: ['large', 'regular', 'small'],
      description: 'The size of the input'
    },
    validateOn: {
      control: { type: 'select' },
      options: ['blur', 'change'],
      description: 'When to trigger validation'
    },
    disabled: { control: 'boolean', description: 'Whether the input is disabled' },
    onChange: { action: 'changed', description: 'Callback fired when the email changes' },
  },
};

export default meta;
type Story = StoryObj<typeof SpEmailInput>;

export const LargePrimary: Story = {
  name: 'Primary (Large)',
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Email Address',
    helperText: 'We\'ll never share your email with anyone else',
    disabled: false,
    validateOn: 'blur',
  },
};

export const RegularPrimary: Story = {
  name: 'Primary (Regular)',
  args: {
    variant: 'primary' as const,
    size: 'regular' as const,
    label: 'Email Address',
    disabled: false,
    validateOn: 'blur' as const,
  },
};

export const SmallPrimary: Story = {
  name: 'Primary (Small)',
  args: {
    variant: 'primary' as const,
    size: 'small' as const,
    label: 'Email',
    disabled: false,
    validateOn: 'blur' as const,
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary' as const,
    size: 'large' as const,
    label: 'Contact Email',
    helperText: 'Optional contact information',
    disabled: false,
    validateOn: 'blur' as const,
  },
};

export const ValidateOnChange: Story = {
  name: 'Validate on Change',
  args: {
    variant: 'primary' as const,
    size: 'large' as const,
    label: 'Email Address',
    helperText: 'Validation happens as you type',
    validateOn: 'change' as const,
    disabled: false,
  },
};

export const WithValidEmail: Story = {
  name: 'With Valid Email',
  args: {
    variant: 'primary' as const,
    size: 'large' as const,
    label: 'Email Address',
    value: '<EMAIL>',
    helperText: 'Email looks good!',
    disabled: false,
    validateOn: 'blur' as const,
  },
};

export const Disabled: Story = {
  args: {
    variant: 'primary' as const,
    size: 'large' as const,
    label: 'Email Address',
    value: '<EMAIL>',
    disabled: true,
    validateOn: 'blur' as const,
  },
};

export const CustomValidation: Story = {
  name: 'Custom Validation',
  args: {
    variant: 'primary' as const,
    size: 'large' as const,
    label: 'Work Email',
    helperText: 'Only company emails are allowed',
    customValidation: (email: string) => {
      if (!email) return 'Email is required';
      if (!email.includes('@company.com')) {
        return 'Please use your company email address';
      }
      return null;
    },
    disabled: false,
    validateOn: 'blur' as const,
  },
};

export const NoLabel: Story = {
  name: 'Without Label',
  args: {
    variant: 'primary' as const,
    size: 'large' as const,
    placeholder: 'Enter your email...',
    disabled: false,
    validateOn: 'blur' as const,
  },
};
