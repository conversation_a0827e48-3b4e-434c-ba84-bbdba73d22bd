import type { Meta, StoryObj } from '@storybook/react';
import { SpEmailInput } from '../SpEmailInput';

const meta: Meta<typeof SpEmailInput> = {
  title: 'Components/SpEmailInput',
  component: SpEmailInput,
  tags: ['autodocs'],
  argTypes: {
    value: { control: 'text', description: 'The current email value' },
    placeholder: { control: 'text', description: 'Placeholder text for the email input' },
    label: { control: 'text', description: 'Label for the email input field' },
    helperText: { control: 'text', description: 'Helper text displayed below the input' },
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
      description: 'The visual style of the input'
    },
    size: {
      control: { type: 'select' },
      options: ['large', 'regular', 'small'],
      description: 'The size of the input'
    },
    validateOn: {
      control: { type: 'select' },
      options: ['blur', 'change'],
      description: 'When to trigger validation'
    },
    disabled: { control: 'boolean', description: 'Whether the input is disabled' },
    onChange: { action: 'changed', description: 'Callback fired when the email changes' },
  },
};

export default meta;
type Story = StoryObj<typeof SpEmailInput>;

export const LargePrimary: Story = {
  name: 'Primary (Large)',
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Email Address',
    helperText: 'We\'ll never share your email with anyone else',
    disabled: false,
    validateOn: 'blur',
  },
};

export const RegularPrimary: Story = {
  name: 'Primary (Regular)',
  args: {
    variant: 'primary',
    size: 'regular',
    label: 'Email Address',
    disabled: false,
    validateOn: 'blur',
  },
};

export const SmallPrimary: Story = {
  name: 'Primary (Small)',
  args: {
    variant: 'primary',
    size: 'small',
    label: 'Email',
    disabled: false,
    validateOn: 'blur',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    size: 'large',
    label: 'Contact Email',
    helperText: 'Optional contact information',
    disabled: false,
    validateOn: 'blur',
  },
};

export const ValidateOnChange: Story = {
  name: 'Validate on Change',
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Email Address',
    helperText: 'Validation happens as you type',
    validateOn: 'change',
    disabled: false,
  },
};

export const WithValidEmail: Story = {
  name: 'With Valid Email',
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Email Address',
    value: '<EMAIL>',
    helperText: 'Email looks good!',
    disabled: false,
    validateOn: 'blur',
  },
};

export const Disabled: Story = {
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Email Address',
    value: '<EMAIL>',
    disabled: true,
    validateOn: 'blur',
  },
};

export const CustomValidation: Story = {
  name: 'Custom Validation',
  args: {
    variant: 'primary',
    size: 'large',
    label: 'Work Email',
    helperText: 'Only company emails are allowed',
    customValidation: (email: string) => {
      if (!email) return 'Email is required';
      if (!email.includes('@company.com')) {
        return 'Please use your company email address';
      }
      return null;
    },
    disabled: false,
    validateOn: 'blur',
  },
};

export const NoLabel: Story = {
  name: 'Without Label',
  args: {
    variant: 'primary',
    size: 'large',
    placeholder: 'Enter your email...',
    disabled: false,
    validateOn: 'blur',
  },
};
