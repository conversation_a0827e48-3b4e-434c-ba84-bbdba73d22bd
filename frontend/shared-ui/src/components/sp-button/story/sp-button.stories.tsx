import type { Meta, StoryObj } from '@storybook/react';
import { SpButton } from '../SpButton';

const meta: Meta<typeof SpButton> = {
  title: 'Components/SpButton',
  component: SpButton,
  tags: ['autodocs'],
  argTypes: {
    children: { control: 'text', description: 'The content of the button' },
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
      description: 'The visual style of the button'
    },
    size: {
      control: { type: 'select' },
      options: ['large', 'regular', 'small'],
      description: 'The size of the button'
    },
    disabled: { control: 'boolean', description: 'The disabled state' },
    onClick: { action: 'clicked', description: 'Click event handler' },
  },
};

export default meta;
type Story = StoryObj<typeof SpButton>;

export const LargePrimary: Story = {
  name: 'Primary (Large)',
  args: {
    variant: 'primary',
    size: 'large',
    children: 'Main Action',
    disabled: false,
  },
};

export const RegularPrimary: Story = {
  name: 'Primary (Regular)',
  args: {
    variant: 'primary',
    size: 'regular',
    children: 'Main Action',
    disabled: false,
  },
};

export const SmallPrimary: Story = {
  name: 'Primary (Small)',
  args: {
    variant: 'primary',
    size: 'small',
    children: 'Main Action',
    disabled: false,
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    size: 'large',
    children: 'Secondary Action',
    disabled: false,
  },
};

export const Disabled: Story = {
  args: {
    variant: 'primary',
    size: 'large',
    children: 'Disabled Action',
    disabled: true,
  },
};
